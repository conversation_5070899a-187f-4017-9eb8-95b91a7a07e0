/* CSS Custom Properties for AMOLED Black Theme */
:root {
  /* AMOLED True Black Base */
  --amoled-black: #000000;
  --amoled-near-black: #0a0a0a;
  --amoled-dark-gray: #111111;

  /* Glass Effects with AMOLED optimization */
  --glass-bg: rgba(255, 255, 255, 0.03);
  --glass-bg-strong: rgba(255, 255, 255, 0.06);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
  --glass-blur: blur(16px);
  --glass-blur-strong: blur(24px);

  /* AMOLED Background Gradients */
  --dark-bg-primary: var(--amoled-black);
  --dark-bg-secondary: var(--amoled-near-black);
  --dark-bg-gradient: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #111111 100%);

  /* High Contrast Text for AMOLED */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-muted: rgba(255, 255, 255, 0.7);

  /* Neon Accent Colors */
  --neon-cyan: #00ffff;
  --neon-blue: #0080ff;
  --neon-purple: #8000ff;
  --neon-pink: #ff00ff;
  --neon-green: #00ff80;
  --neon-yellow: #ffff00;

  /* Enhanced Accent Colors */
  --accent-blue: var(--neon-blue);
  --accent-purple: var(--neon-purple);
  --accent-cyan: var(--neon-cyan);

  /* Glow Effects */
  --glow-cyan: 0 0 20px rgba(0, 255, 255, 0.5);
  --glow-blue: 0 0 20px rgba(0, 128, 255, 0.5);
  --glow-purple: 0 0 20px rgba(128, 0, 255, 0.5);
  --glow-pink: 0 0 20px rgba(255, 0, 255, 0.5);
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--amoled-black);
  min-height: 100vh;
  color: var(--text-primary);
  overflow-x: hidden;
}

.app-container {
  min-height: 100vh;
  padding: 20px;
  position: relative;
  background: var(--amoled-black);
}

.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgba(0, 128, 255, 0.04) 0%, transparent 60%),
    radial-gradient(circle at 60% 70%, rgba(128, 0, 255, 0.05) 0%, transparent 60%);
  pointer-events: none;
  z-index: -1;
  animation: amoledGlow 8s ease-in-out infinite alternate;
}

@keyframes amoledGlow {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.header {
  text-align: center;
  margin-bottom: 40px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--neon-cyan);
  border-radius: 24px;
  padding: 30px;
  box-shadow: var(--glass-shadow), var(--glow-cyan);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.header:hover {
  border-color: var(--neon-blue);
  box-shadow: var(--glass-shadow), var(--glow-blue);
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  animation: neonPulse 2s ease-in-out infinite alternate;
}

@keyframes neonPulse {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

.header h1 {
  font-size: 3rem;
  color: var(--text-primary);
  margin: 0 0 10px 0;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5), 0 2px 10px rgba(0, 0, 0, 0.8);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% { text-shadow: 0 0 20px rgba(0, 255, 255, 0.3), 0 2px 10px rgba(0, 0, 0, 0.8); }
  100% { text-shadow: 0 0 30px rgba(0, 255, 255, 0.7), 0 2px 10px rgba(0, 0, 0, 0.8); }
}

.header p {
  color: var(--text-secondary);
  font-size: 1.2rem;
  margin: 0;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.table-container {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--neon-purple);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: var(--glass-shadow), var(--glow-purple);
  position: relative;
  transition: all 0.3s ease;
}

.table-container:hover {
  border-color: var(--neon-cyan);
  box-shadow: var(--glass-shadow), var(--glow-cyan);
}

.table-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-purple), transparent);
  animation: neonPulse 2s ease-in-out infinite alternate;
}

.stats-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(0, 255, 255, 0.03);
  border-bottom: 1px solid var(--neon-cyan);
  color: var(--text-primary);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.stats-info button {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  padding: 10px 20px;
  border-radius: 12px;
  cursor: pointer;
  margin-left: 15px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.stats-info button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.stats-info button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: transparent;
}

.table th {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: var(--text-primary);
  padding: 15px;
  text-align: center;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--glass-border);
  position: relative;
}

.table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
}

.table td {
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  background: transparent;
}

.table tr:hover {
  background: rgba(0, 255, 255, 0.08);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  box-shadow: inset 0 0 20px rgba(0, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.table tr:nth-child(even) {
  background: rgba(255, 255, 255, 0.01);
}

.rank-badge {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: var(--neon-cyan);
  border: 1px solid var(--neon-cyan);
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: bold;
  font-size: 12px;
  min-width: 30px;
  display: inline-block;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.rank-badge:hover {
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
  transform: scale(1.05);
}

.crypto-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
  margin-right: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8), var(--glow-blue);
  border: 1px solid var(--neon-cyan);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.crypto-icon:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8), var(--glow-purple);
  transform: scale(1.1);
}

.symbol-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: var(--text-primary);
}

.signal-badge {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 12px;
  text-transform: uppercase;
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
  text-shadow: 0 0 8px currentColor;
  transition: all 0.3s ease;
}

.signal-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.1;
  z-index: -1;
}

.clickable-signal {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-signal:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.8), 0 0 20px currentColor;
  border-color: currentColor;
  text-shadow: 0 0 15px currentColor;
}

.signal-gold {
  background: rgba(255, 255, 0, 0.15);
  color: var(--neon-yellow);
  border-color: var(--neon-yellow);
  box-shadow: 0 0 15px rgba(255, 255, 0, 0.3);
}
.signal-green {
  background: rgba(0, 255, 128, 0.15);
  color: var(--neon-green);
  border-color: var(--neon-green);
  box-shadow: 0 0 15px rgba(0, 255, 128, 0.3);
}
.signal-blue {
  background: rgba(0, 128, 255, 0.15);
  color: var(--neon-blue);
  border-color: var(--neon-blue);
  box-shadow: 0 0 15px rgba(0, 128, 255, 0.3);
}
.signal-red {
  background: rgba(255, 0, 128, 0.15);
  color: var(--neon-pink);
  border-color: var(--neon-pink);
  box-shadow: 0 0 15px rgba(255, 0, 128, 0.3);
}
.signal-gray {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

.loading-container {
  text-align: center;
  padding: 60px 40px;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  margin: 40px auto;
  max-width: 600px;
  box-shadow: var(--glass-shadow);
  color: var(--text-primary);
}

/* Chart Modal Styles */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: var(--glass-blur);
  }
}

.chart-modal-content {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur-strong);
  -webkit-backdrop-filter: var(--glass-blur-strong);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  padding: 24px;
  max-width: 95vw;
  max-height: 75vh;
  width: 95vw;
  height: 75vh;
  box-shadow: var(--glass-shadow);
  position: relative;
  display: flex;
  flex-direction: column;
  color: var(--text-primary);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.chart-modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--glass-border);
  position: relative;
}

.chart-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
}

.chart-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.3rem;
  font-weight: 600;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.close-button {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  font-size: 20px;
  cursor: pointer;
  color: var(--text-primary);
  padding: 0;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.chart-container {
  margin: 20px 0;
  width: 100%;
  min-width: 600px;
  flex: 1;
  min-height: 400px;
  background: var(--amoled-black);
  border-radius: 16px;
  border: 1px solid var(--neon-cyan);
  overflow: hidden;
  position: relative;
  box-shadow: var(--glow-cyan), inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  animation: neonPulse 2s ease-in-out infinite alternate;
}

.chart-legend {
  display: flex;
  gap: 24px;
  margin-top: 20px;
  justify-content: center;
  flex-wrap: wrap;
  padding: 16px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .app-container {
    padding: 15px;
  }

  .header {
    padding: 20px;
    margin-bottom: 30px;
  }

  .header h1 {
    font-size: 2.2rem;
  }

  .header p {
    font-size: 1rem;
  }

  .stats-info {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 16px;
  }

  .table th, .table td {
    padding: 10px 6px;
    font-size: 11px;
  }

  .chart-modal-content {
    margin: 10px;
    max-width: calc(100vw - 20px);
    width: calc(100vw - 20px);
    max-height: calc(75vh - 20px);
    height: calc(75vh - 20px);
    padding: 16px;
    border-radius: 20px;
  }

  .chart-container {
    min-width: 280px;
    min-height: 280px;
    margin: 15px 0;
  }

  .chart-legend {
    gap: 12px;
    padding: 12px;
    margin-top: 15px;
  }

  .legend-item {
    font-size: 11px;
  }

  .signal-badge {
    padding: 6px 12px;
    font-size: 10px;
  }

  .crypto-icon {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }
}

/* Loading State Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--accent-cyan);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  color: var(--text-secondary);
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

.loading-small .loading-spinner {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.loading-large .loading-spinner {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

/* Error State Styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
  color: var(--accent-red, #ef4444);
}

.error-message {
  color: #ef4444;
  font-size: 16px;
  margin: 0 0 20px 0;
  max-width: 400px;
  font-weight: 500;
}

.retry-button {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Error Boundary Styles */
.error-boundary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--neon-pink);
  border-radius: 16px;
  margin: 20px;
  box-shadow: var(--glass-shadow), var(--glow-pink);
}

.error-boundary h3 {
  color: var(--neon-pink);
  margin: 0 0 10px 0;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
}

.error-boundary p {
  color: var(--text-secondary);
  margin: 0 0 20px 0;
}

/* AMOLED Specific Enhancements */
.table th {
  background: rgba(0, 255, 255, 0.05);
  color: var(--text-primary);
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  border-bottom: 1px solid var(--neon-cyan);
}

.table td {
  color: var(--text-primary);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Scrollbar Styling for AMOLED */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--amoled-black);
}

::-webkit-scrollbar-thumb {
  background: var(--neon-cyan);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-blue);
  box-shadow: 0 0 15px rgba(0, 128, 255, 0.7);
}

/* Selection styling */
::selection {
  background: rgba(0, 255, 255, 0.3);
  color: var(--text-primary);
}

::-moz-selection {
  background: rgba(0, 255, 255, 0.3);
  color: var(--text-primary);
}
