import { ColorType } from "lightweight-charts";

export type ChartTheme = "light" | "dark";

export const CHART_THEMES = {
  light: {
    layout: {
      background: { type: ColorType.Solid, color: "#ffffff" },
      textColor: "#333",
    },
    grid: {
      vertLines: { color: "#e1e1e1" },
      horzLines: { color: "#e1e1e1" },
    },
    rightPriceScale: {
      borderColor: "#cccccc",
    },
    crosshair: {
      vertLine: {
        color: "#758696",
        labelBackgroundColor: "#4c525e",
      },
      horzLine: {
        color: "#758696",
        labelBackgroundColor: "#4c525e",
      },
    },
  },
  dark: {
    layout: {
      background: { type: ColorType.Solid, color: "#000000" },
      textColor: "#ffffff",
    },
    grid: {
      vertLines: { color: "#111111" },
      horzLines: { color: "#111111" },
    },
    rightPriceScale: {
      borderColor: "#00ffff",
    },
    crosshair: {
      vertLine: {
        color: "#00ffff88",
        labelBackgroundColor: "#00ffff",
      },
      horzLine: {
        color: "#00ffff",
        labelBackgroundColor: "#00ffff",
      },
    },
  },
} as const;

export const DEFAULT_THEME: ChartTheme = "dark";

export const CHART_CONFIG = {
  dimensions: {
    width: 1200,
    height: 600,
  },
  timeScale: {
    timeVisible: true,
    secondsVisible: false,
  },
  crosshair: {
    mode: 1,
  },
  zoom: {
    speed: 1 / 30, // Faster zoom speed (1/3 instead of default 1/8)
  },
} as const;

export const SERIES_THEMES = {
  light: {
    candlestick: {
      upColor: "#26a69a",
      downColor: "#ef5350",
      borderVisible: false,
      wickUpColor: "#26a69a",
      wickDownColor: "#ef5350",
    },
    smma: {
      15: { color: "#FF6B35", lineWidth: 1 },
      19: { color: "#F7931E", lineWidth: 1 },
      25: { color: "#FFD23F", lineWidth: 2 },
      29: { color: "#2196F3", lineWidth: 2 },
    },
  },
  dark: {
    candlestick: {
      upColor: "#00ff80",
      downColor: "#ff0080",
      borderVisible: false,
      wickUpColor: "#00ff80",
      wickDownColor: "#ff0080",
    },
    smma: {
      15: { color: "#00ffff", lineWidth: 2 },
      19: { color: "#ffff00", lineWidth: 1 },
      25: { color: "#ff00ff", lineWidth: 2 },
      29: { color: "#0080ff", lineWidth: 2 },
    },
  },
} as const;

export const CHART_LEGEND_THEMES = {
  light: [
    { color: "#26a69a", label: "Price (Candlestick)" },
    { color: "#FF6B35", label: "SMMA-15" },
    { color: "#2196F3", label: "SMMA-29" },
  ],
  dark: [
    { color: "#54B6D7", label: "Price (Candlestick)" },
    { color: "#FF8A65", label: "SMMA-15" },
    { color: "#64B5F6", label: "SMMA-29" },
  ],
} as const;

// Helper functions to get theme-specific configurations
export const getChartConfig = (theme: ChartTheme = DEFAULT_THEME) => ({
  ...CHART_CONFIG,
  ...CHART_THEMES[theme],
});

export const getSeriesConfig = (theme: ChartTheme = DEFAULT_THEME) =>
  SERIES_THEMES[theme];
