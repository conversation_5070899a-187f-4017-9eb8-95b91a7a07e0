import type { ChartData, CandlestickData } from '../types/chart';

export const dataTransformers = {
  /**
   * Transform indicator values to candlestick data format
   */
  transformCandlestickData: (indicatorValues: any[]): CandlestickData[] => {
    return indicatorValues
      .filter(item => item.open && item.high && item.low && item.close)
      .map(item => ({
        time: item.timestamp.split('T')[0],
        open: item.open!,
        high: item.high!,
        low: item.low!,
        close: item.close!,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Transform indicator values to SMMA line data format
   */
  transformSMMAData: (indicatorValues: any[], period: number): ChartData[] => {
    const fieldName = `smma_${period}`;

    return indicatorValues
      .filter(item => (item as any)[fieldName] !== undefined && (item as any)[fieldName] !== null)
      .map(item => ({
        time: item.timestamp.split('T')[0],
        value: (item as any)[fieldName] as number,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Transform indicator values to area data for the upper bound (SMMA-29) for a specific color
   */
  transformUpperAreaData: (indicatorValues: any[], color: 'gold' | 'blue' | 'gray'): ChartData[] => {
    return indicatorValues
      .filter(item =>
        item.color === color &&
        item.smma_15 !== undefined && item.smma_15 !== null &&
        item.smma_29 !== undefined && item.smma_29 !== null
      )
      .map(item => ({
        time: item.timestamp.split('T')[0],
        // Use the higher SMMA value
        value: Math.max(item.smma_15, item.smma_29),
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Transform indicator values to area data for the lower bound (SMMA-15) for a specific color
   */
  transformLowerAreaData: (indicatorValues: any[], color: 'gold' | 'blue' | 'gray'): ChartData[] => {
    return indicatorValues
      .filter(item =>
        item.color === color &&
        item.smma_15 !== undefined && item.smma_15 !== null &&
        item.smma_29 !== undefined && item.smma_29 !== null
      )
      .map(item => ({
        time: item.timestamp.split('T')[0],
        // Use the lower SMMA value
        value: Math.min(item.smma_15, item.smma_29),
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },

  /**
   * Generic data transformer for any numeric field
   */
  transformIndicatorData: (
    indicatorValues: any[],
    field: string
  ): ChartData[] => {
    return indicatorValues
      .filter(item => (item as any)[field] !== undefined && (item as any)[field] !== null)
      .map(item => ({
        time: item.timestamp.split('T')[0],
        value: (item as any)[field] as number,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  },
};
